{"www.huhhothome.cn": {"certificate_chain": {"success": false, "error": "'SSLSocket' object has no attribute 'getpeercert_chain'", "error_type": "other"}, "certificate_trust": {"trusted": true, "error": null}, "supported_protocols": {"TLSv1.3": {"supported": false, "error": "[SSL: TLSV1_ALERT_PROTOCOL_VERSION] tlsv1 alert protocol version (_ssl.c:997)"}, "TLSv1.2": {"supported": true, "version": "TLSv1.2", "cipher": ["ECDHE-RSA-AES256-GCM-SHA384", "TLSv1.2", 256]}, "TLSv1.1": {"supported": false, "error": "[SSL: NO_CIPHERS_AVAILABLE] no ciphers available (_ssl.c:997)"}, "TLSv1.0": {"supported": false, "error": "[SSL: NO_CIPHERS_AVAILABLE] no ciphers available (_ssl.c:997)"}}, "certificate_transparency": {"ct_logs_found": 0, "error": "HTTPSConnectionPool(host='crt.sh', port=443): Max retries exceeded with url: /?q=www.huhhothome.cn&output=json (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:997)')))"}, "security_analysis": {"security_issues": [], "recommendations": [], "security_score": 100}}, "api.huhhothome.cn": {"certificate_chain": {"success": false, "error": "'SSLSocket' object has no attribute 'getpeercert_chain'", "error_type": "other"}, "certificate_trust": {"trusted": false, "error": "[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'api.huhhothome.cn'. (_ssl.c:997)", "error_type": "verification_failed"}, "supported_protocols": {"TLSv1.3": {"supported": false, "error": "[SSL: TLSV1_ALERT_PROTOCOL_VERSION] tlsv1 alert protocol version (_ssl.c:997)"}, "TLSv1.2": {"supported": true, "version": "TLSv1.2", "cipher": ["ECDHE-RSA-AES256-GCM-SHA384", "TLSv1.2", 256]}, "TLSv1.1": {"supported": false, "error": "[SSL: NO_CIPHERS_AVAILABLE] no ciphers available (_ssl.c:997)"}, "TLSv1.0": {"supported": false, "error": "[SSL: NO_CIPHERS_AVAILABLE] no ciphers available (_ssl.c:997)"}}, "certificate_transparency": {"ct_logs_found": 0, "error": "HTTPSConnectionPool(host='crt.sh', port=443): Max retries exceeded with url: /?q=api.huhhothome.cn&output=json (Caused by ProxyError('Cannot connect to proxy.', TimeoutError('_ssl.c:980: The handshake operation timed out')))"}, "security_analysis": {"security_issues": ["证书不受信任"], "recommendations": ["需要配置证书例外或使用受信任的证书"], "security_score": 80}}, "huhhothome.cn": {"certificate_chain": {"success": false, "error": "'SSLSocket' object has no attribute 'getpeercert_chain'", "error_type": "other"}, "certificate_trust": {"trusted": true, "error": null}, "supported_protocols": {"TLSv1.3": {"supported": false, "error": "[SSL: TLSV1_ALERT_PROTOCOL_VERSION] tlsv1 alert protocol version (_ssl.c:997)"}, "TLSv1.2": {"supported": true, "version": "TLSv1.2", "cipher": ["ECDHE-RSA-AES256-GCM-SHA384", "TLSv1.2", 256]}, "TLSv1.1": {"supported": false, "error": "[SSL: NO_CIPHERS_AVAILABLE] no ciphers available (_ssl.c:997)"}, "TLSv1.0": {"supported": false, "error": "[SSL: NO_CIPHERS_AVAILABLE] no ciphers available (_ssl.c:997)"}}, "certificate_transparency": {"ct_logs_found": 12, "logs": [{"issuer_ca_id": 64135, "issuer_name": "C=US, O=DigiCert Inc, OU=www.digicert.com, CN=Encryption Everywhere DV TLS CA - G2", "common_name": "www.huhhothome.cn", "name_value": "huhhothome.cn\nwww.huhhothome.cn", "id": 19010484056, "entry_timestamp": "2025-06-14T04:22:13.902", "not_before": "2024-06-13T00:00:00", "not_after": "2025-06-13T23:59:59", "serial_number": "092d2f3818889facd85e6ae73cbb51d1", "result_count": 3}, {"issuer_ca_id": 64135, "issuer_name": "C=US, O=DigiCert Inc, OU=www.digicert.com, CN=Encryption Everywhere DV TLS CA - G2", "common_name": "www.huhhothome.cn", "name_value": "huhhothome.cn\nwww.huhhothome.cn", "id": 19010483590, "entry_timestamp": "2025-06-14T04:22:10.211", "not_before": "2025-06-14T00:00:00", "not_after": "2026-06-13T23:59:59", "serial_number": "012778aa72bac4e4a3b8f730c2b2516e", "result_count": 3}, {"issuer_ca_id": 64135, "issuer_name": "C=US, O=DigiCert Inc, OU=www.digicert.com, CN=Encryption Everywhere DV TLS CA - G2", "common_name": "www.huhhothome.cn", "name_value": "huhhothome.cn\nwww.huhhothome.cn", "id": 13373022948, "entry_timestamp": "2024-06-13T02:00:11.454", "not_before": "2024-06-13T00:00:00", "not_after": "2025-06-13T23:59:59", "serial_number": "092d2f3818889facd85e6ae73cbb51d1", "result_count": 3}, {"issuer_ca_id": 64135, "issuer_name": "C=US, O=DigiCert Inc, OU=www.digicert.com, CN=Encryption Everywhere DV TLS CA - G2", "common_name": "huhhothome.cn", "name_value": "huhhothome.cn\nwww.huhhothome.cn", "id": 13282453027, "entry_timestamp": "2024-06-04T09:15:05.328", "not_before": "2024-06-04T00:00:00", "not_after": "2024-09-01T23:59:59", "serial_number": "0def0249f02e435c973e6bc514ae2d61", "result_count": 3}, {"issuer_ca_id": 231722, "issuer_name": "C=CN, O=\"TrustAsia Technologies, Inc.\", CN=TrustAsia RSA DV TLS CA G2", "common_name": "huhhothome.cn", "name_value": "huhhothome.cn\nwww.huhhothome.cn", "id": 9647145580, "entry_timestamp": "2023-06-14T02:57:56.659", "not_before": "2023-06-14T00:00:00", "not_after": "2024-06-13T23:59:59", "serial_number": "143719e131bb2fffb8a79451dcd48367", "result_count": 3}]}, "security_analysis": {"security_issues": [], "recommendations": [], "security_score": 100}}}