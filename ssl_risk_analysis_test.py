#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL风险分析测试脚本
验证SSL验证启用后的潜在风险和兼容性问题
"""

import ssl
import socket
import requests
import aiohttp
import asyncio
import time
import json
import sys
import os
from datetime import datetime, timedelta
from urllib.parse import urlparse
import warnings
warnings.filterwarnings('ignore', category=DeprecationWarning)

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SSLRiskAnalyzer:
    """SSL风险分析器"""
    
    def __init__(self):
        self.target_urls = [
            'https://www.huhhothome.cn',
            'https://www.huhhothome.cn/api/dynamicapi/apiview/viewdata',
            'https://api.huhhothome.cn'
        ]
        self.test_results = {}
        self.proxy_test_urls = [
            'http://httpbin.org/ip',
            'https://httpbin.org/ip'
        ]
        
    def print_header(self):
        """打印测试头部信息"""
        print("=" * 80)
        print("🔒 青城住房监控系统 - SSL风险分析测试")
        print("=" * 80)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"目标网站: {', '.join(self.target_urls)}")
        print("-" * 80)
    
    def test_ssl_certificate_info(self, url):
        """测试SSL证书信息"""
        print(f"\n🔍 分析SSL证书: {url}")
        
        try:
            parsed_url = urlparse(url)
            hostname = parsed_url.hostname
            port = parsed_url.port or 443
            
            # 创建SSL上下文
            context = ssl.create_default_context()
            
            # 获取证书信息
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    cert = ssock.getpeercert()
                    cipher = ssock.cipher()
                    
            # 分析证书信息
            cert_info = {
                'subject': dict(x[0] for x in cert['subject']),
                'issuer': dict(x[0] for x in cert['issuer']),
                'version': cert['version'],
                'serial_number': cert['serialNumber'],
                'not_before': cert['notBefore'],
                'not_after': cert['notAfter'],
                'cipher_suite': cipher[0] if cipher else None,
                'tls_version': cipher[1] if cipher else None,
                'key_bits': cipher[2] if cipher else None
            }
            
            # 检查证书有效期
            not_after = datetime.strptime(cert['notAfter'], '%b %d %H:%M:%S %Y %Z')
            days_until_expiry = (not_after - datetime.now()).days
            
            print(f"  ✅ 证书主体: {cert_info['subject'].get('commonName', 'N/A')}")
            print(f"  ✅ 证书颁发者: {cert_info['issuer'].get('organizationName', 'N/A')}")
            print(f"  ✅ 有效期至: {cert['notAfter']} (剩余{days_until_expiry}天)")
            print(f"  ✅ TLS版本: {cert_info['tls_version']}")
            print(f"  ✅ 加密套件: {cert_info['cipher_suite']}")
            
            # 检查SAN (Subject Alternative Names)
            if 'subjectAltName' in cert:
                san_list = [name[1] for name in cert['subjectAltName']]
                print(f"  ✅ SAN域名: {', '.join(san_list)}")
            
            return {
                'success': True,
                'cert_info': cert_info,
                'days_until_expiry': days_until_expiry,
                'warnings': []
            }
            
        except ssl.SSLError as e:
            print(f"  ❌ SSL错误: {e}")
            return {'success': False, 'error': f'SSL错误: {e}', 'error_type': 'ssl_error'}
        except socket.timeout:
            print(f"  ❌ 连接超时")
            return {'success': False, 'error': '连接超时', 'error_type': 'timeout'}
        except Exception as e:
            print(f"  ❌ 其他错误: {e}")
            return {'success': False, 'error': str(e), 'error_type': 'other'}
    
    def test_ssl_with_requests(self, url):
        """测试requests库的SSL验证"""
        print(f"\n🌐 测试requests SSL验证: {url}")
        
        results = {}
        
        # 测试SSL验证启用
        try:
            start_time = time.time()
            response = requests.get(url, verify=True, timeout=10)
            end_time = time.time()
            
            results['ssl_enabled'] = {
                'success': True,
                'status_code': response.status_code,
                'response_time': round(end_time - start_time, 3),
                'headers': dict(response.headers)
            }
            print(f"  ✅ SSL验证启用: 状态码{response.status_code}, 响应时间{results['ssl_enabled']['response_time']}s")
            
        except requests.exceptions.SSLError as e:
            results['ssl_enabled'] = {'success': False, 'error': str(e), 'error_type': 'ssl_error'}
            print(f"  ❌ SSL验证失败: {e}")
        except requests.exceptions.Timeout:
            results['ssl_enabled'] = {'success': False, 'error': '请求超时', 'error_type': 'timeout'}
            print(f"  ❌ 请求超时")
        except Exception as e:
            results['ssl_enabled'] = {'success': False, 'error': str(e), 'error_type': 'other'}
            print(f"  ❌ 其他错误: {e}")
        
        # 测试SSL验证禁用（对比）
        try:
            start_time = time.time()
            response = requests.get(url, verify=False, timeout=10)
            end_time = time.time()
            
            results['ssl_disabled'] = {
                'success': True,
                'status_code': response.status_code,
                'response_time': round(end_time - start_time, 3)
            }
            print(f"  ✅ SSL验证禁用: 状态码{response.status_code}, 响应时间{results['ssl_disabled']['response_time']}s")
            
        except Exception as e:
            results['ssl_disabled'] = {'success': False, 'error': str(e)}
            print(f"  ❌ SSL禁用也失败: {e}")
        
        return results
    
    async def test_ssl_with_aiohttp(self, url):
        """测试aiohttp的SSL验证"""
        print(f"\n🚀 测试aiohttp SSL验证: {url}")
        
        results = {}
        
        # 测试SSL验证启用
        try:
            connector = aiohttp.TCPConnector(ssl=True)
            timeout = aiohttp.ClientTimeout(total=10)
            
            async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
                start_time = time.time()
                async with session.get(url) as response:
                    end_time = time.time()
                    content = await response.text()
                    
                    results['ssl_enabled'] = {
                        'success': True,
                        'status_code': response.status,
                        'response_time': round(end_time - start_time, 3),
                        'content_length': len(content)
                    }
                    print(f"  ✅ SSL验证启用: 状态码{response.status}, 响应时间{results['ssl_enabled']['response_time']}s")
                    
        except aiohttp.ClientSSLError as e:
            results['ssl_enabled'] = {'success': False, 'error': str(e), 'error_type': 'ssl_error'}
            print(f"  ❌ SSL验证失败: {e}")
        except asyncio.TimeoutError:
            results['ssl_enabled'] = {'success': False, 'error': '请求超时', 'error_type': 'timeout'}
            print(f"  ❌ 请求超时")
        except Exception as e:
            results['ssl_enabled'] = {'success': False, 'error': str(e), 'error_type': 'other'}
            print(f"  ❌ 其他错误: {e}")
        
        # 测试SSL验证禁用（对比）
        try:
            connector = aiohttp.TCPConnector(ssl=False)
            timeout = aiohttp.ClientTimeout(total=10)
            
            async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
                start_time = time.time()
                async with session.get(url) as response:
                    end_time = time.time()
                    
                    results['ssl_disabled'] = {
                        'success': True,
                        'status_code': response.status,
                        'response_time': round(end_time - start_time, 3)
                    }
                    print(f"  ✅ SSL验证禁用: 状态码{response.status}, 响应时间{results['ssl_disabled']['response_time']}s")
                    
        except Exception as e:
            results['ssl_disabled'] = {'success': False, 'error': str(e)}
            print(f"  ❌ SSL禁用也失败: {e}")
        
        return results
    
    def test_proxy_ssl_compatibility(self):
        """测试代理SSL兼容性"""
        print(f"\n🔄 测试代理SSL兼容性")
        
        # 这里使用免费代理进行测试（实际使用时需要替换为真实代理）
        test_proxies = [
            # 可以添加一些免费代理进行测试
            # 'http://proxy1:port',
            # 'http://proxy2:port'
        ]
        
        if not test_proxies:
            print("  ⚠️  未配置测试代理，跳过代理SSL兼容性测试")
            return {'skipped': True, 'reason': '未配置测试代理'}
        
        results = {}
        for proxy in test_proxies:
            print(f"  测试代理: {proxy}")
            
            # 测试HTTP代理 + HTTPS目标
            try:
                proxies = {'http': proxy, 'https': proxy}
                response = requests.get('https://httpbin.org/ip', 
                                      proxies=proxies, 
                                      verify=True, 
                                      timeout=10)
                results[proxy] = {
                    'success': True,
                    'status_code': response.status_code,
                    'response_data': response.json()
                }
                print(f"    ✅ 代理SSL测试成功")
                
            except Exception as e:
                results[proxy] = {'success': False, 'error': str(e)}
                print(f"    ❌ 代理SSL测试失败: {e}")
        
        return results
    
    def analyze_ssl_risks(self):
        """分析SSL风险"""
        print(f"\n📊 SSL风险分析汇总")
        
        risks = []
        recommendations = []
        
        # 分析证书风险
        for url, result in self.test_results.items():
            if 'cert_info' in result:
                cert_result = result['cert_info']
                if not cert_result['success']:
                    if cert_result.get('error_type') == 'ssl_error':
                        risks.append(f"❌ {url}: SSL证书验证失败")
                        recommendations.append(f"建议为 {url} 配置证书例外或使用自定义CA")
                
                elif cert_result.get('days_until_expiry', 0) < 30:
                    risks.append(f"⚠️  {url}: 证书即将过期")
                    recommendations.append(f"监控 {url} 的证书更新")
        
        # 分析请求库兼容性
        ssl_failures = 0
        total_tests = 0
        
        for url, result in self.test_results.items():
            if 'requests_test' in result:
                total_tests += 1
                if not result['requests_test'].get('ssl_enabled', {}).get('success', False):
                    ssl_failures += 1
            
            if 'aiohttp_test' in result:
                total_tests += 1
                if not result['aiohttp_test'].get('ssl_enabled', {}).get('success', False):
                    ssl_failures += 1
        
        if ssl_failures > 0:
            failure_rate = (ssl_failures / total_tests) * 100
            risks.append(f"⚠️  SSL验证失败率: {failure_rate:.1f}% ({ssl_failures}/{total_tests})")
            
            if failure_rate > 50:
                recommendations.append("建议实施渐进式SSL启用策略")
            else:
                recommendations.append("可以考虑启用SSL验证，但需要异常处理")
        
        # 输出风险分析结果
        if risks:
            print("  发现的风险:")
            for risk in risks:
                print(f"    {risk}")
        else:
            print("  ✅ 未发现明显SSL风险")
        
        if recommendations:
            print("  建议措施:")
            for rec in recommendations:
                print(f"    • {rec}")
        
        return {'risks': risks, 'recommendations': recommendations}
    
    async def run_all_tests(self):
        """运行所有测试"""
        self.print_header()
        
        # 测试每个目标URL
        for url in self.target_urls:
            print(f"\n{'='*60}")
            print(f"测试目标: {url}")
            print('='*60)
            
            # 证书信息测试
            cert_result = self.test_ssl_certificate_info(url)
            
            # requests测试
            requests_result = self.test_ssl_with_requests(url)
            
            # aiohttp测试
            aiohttp_result = await self.test_ssl_with_aiohttp(url)
            
            # 保存结果
            self.test_results[url] = {
                'cert_info': cert_result,
                'requests_test': requests_result,
                'aiohttp_test': aiohttp_result
            }
        
        # 代理兼容性测试
        proxy_result = self.test_proxy_ssl_compatibility()
        self.test_results['proxy_compatibility'] = proxy_result
        
        # 风险分析
        risk_analysis = self.analyze_ssl_risks()
        self.test_results['risk_analysis'] = risk_analysis
        
        # 保存测试结果到文件
        self.save_results()
        
        print(f"\n{'='*80}")
        print("🎯 测试完成！结果已保存到 ssl_risk_test_results.json")
        print('='*80)
    
    def save_results(self):
        """保存测试结果到JSON文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'ssl_risk_test_results_{timestamp}.json'
        
        # 处理不可序列化的对象
        serializable_results = {}
        for key, value in self.test_results.items():
            serializable_results[key] = self._make_serializable(value)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)
        
        print(f"测试结果已保存到: {filename}")
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, '__dict__'):
            return str(obj)
        else:
            return obj

async def main():
    """主函数"""
    analyzer = SSLRiskAnalyzer()
    await analyzer.run_all_tests()

if __name__ == '__main__':
    # 抑制SSL警告
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    # 运行测试
    asyncio.run(main())
