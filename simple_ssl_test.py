#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化SSL测试脚本 - 快速验证SSL配置策略
"""

import requests
import json
import sys
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def test_ssl_strategies():
    """测试SSL策略"""
    print("=" * 60)
    print("🔒 简化SSL策略测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 60)
    
    # 测试目标
    targets = {
        'main_site': 'https://www.huhhothome.cn',
        'api_endpoint': 'https://www.huhhothome.cn/api/dynamicapi/apiview/viewdata',
        'problematic_api': 'https://api.huhhothome.cn'
    }
    
    results = {}
    
    for name, url in targets.items():
        print(f"\n🌐 测试目标: {name} - {url}")
        
        # 测试SSL启用
        try:
            response = requests.get(url, verify=True, timeout=10)
            ssl_enabled_result = {
                'success': True,
                'status_code': response.status_code,
                'response_time': response.elapsed.total_seconds()
            }
            print(f"  ✅ SSL启用: 状态码{response.status_code}, 响应时间{ssl_enabled_result['response_time']:.3f}s")
        except Exception as e:
            ssl_enabled_result = {
                'success': False,
                'error': str(e)[:200],
                'error_type': type(e).__name__
            }
            print(f"  ❌ SSL启用失败: {type(e).__name__}")
        
        # 测试SSL禁用
        try:
            response = requests.get(url, verify=False, timeout=10)
            ssl_disabled_result = {
                'success': True,
                'status_code': response.status_code,
                'response_time': response.elapsed.total_seconds()
            }
            print(f"  ✅ SSL禁用: 状态码{response.status_code}, 响应时间{ssl_disabled_result['response_time']:.3f}s")
        except Exception as e:
            ssl_disabled_result = {
                'success': False,
                'error': str(e)[:200],
                'error_type': type(e).__name__
            }
            print(f"  ❌ SSL禁用失败: {type(e).__name__}")
        
        results[name] = {
            'url': url,
            'ssl_enabled': ssl_enabled_result,
            'ssl_disabled': ssl_disabled_result
        }
    
    # 分析结果
    print(f"\n📊 测试结果分析")
    print("-" * 40)
    
    ssl_success_count = 0
    total_tests = len(targets)
    
    for name, result in results.items():
        ssl_works = result['ssl_enabled']['success']
        insecure_works = result['ssl_disabled']['success']
        
        if ssl_works:
            ssl_success_count += 1
            print(f"  ✅ {name}: SSL验证可用")
        elif insecure_works:
            print(f"  ⚠️  {name}: 仅不安全连接可用")
        else:
            print(f"  ❌ {name}: 完全无法连接")
    
    success_rate = (ssl_success_count / total_tests) * 100
    print(f"\nSSL验证成功率: {success_rate:.1f}% ({ssl_success_count}/{total_tests})")
    
    # 生成建议
    print(f"\n💡 建议措施:")
    if success_rate >= 80:
        print("  • 可以启用SSL验证，风险较低")
        print("  • 建议为失败的URL配置证书例外")
    elif success_rate >= 50:
        print("  • 建议渐进式启用SSL验证")
        print("  • 需要实现SSL验证失败的降级机制")
    else:
        print("  • SSL验证风险较高，建议保持当前配置")
        print("  • 优先解决证书问题后再启用SSL验证")
    
    # 保存结果
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'simple_ssl_test_results_{timestamp}.json'
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n📄 结果已保存到: {filename}")
    
    return results

def main():
    """主函数"""
    try:
        # 抑制SSL警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # 运行测试
        results = test_ssl_strategies()
        
        print(f"\n🎯 简化SSL测试完成！")
        return 0
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
