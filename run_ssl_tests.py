#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL测试运行器 - 编译和执行SSL风险分析测试
"""

import os
import sys
import subprocess
import time
from datetime import datetime

class SSLTestRunner:
    """SSL测试运行器"""
    
    def __init__(self):
        self.project_root = os.path.dirname(os.path.abspath(__file__))
        self.test_scripts = [
            'ssl_risk_analysis_test.py',
            'ssl_certificate_deep_analysis.py'
        ]
        
    def print_header(self):
        """打印运行器头部"""
        print("=" * 80)
        print("🚀 SSL测试运行器 - 青城住房监控系统")
        print("=" * 80)
        print(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"项目路径: {self.project_root}")
        print("-" * 80)
    
    def check_dependencies(self):
        """检查依赖"""
        print("\n📦 检查依赖...")
        
        required_packages = [
            'requests',
            'aiohttp',
            'ssl',
            'socket',
            'json'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                print(f"  ✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"  ❌ {package} - 缺失")
        
        if missing_packages:
            print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
            print("请运行以下命令安装:")
            print(f"pip install {' '.join(missing_packages)}")
            return False
        
        print("✅ 所有依赖检查通过")
        return True
    
    def check_scripts(self):
        """检查测试脚本"""
        print("\n📄 检查测试脚本...")
        
        missing_scripts = []
        
        for script in self.test_scripts:
            script_path = os.path.join(self.project_root, script)
            if os.path.exists(script_path):
                print(f"  ✅ {script}")
            else:
                missing_scripts.append(script)
                print(f"  ❌ {script} - 文件不存在")
        
        if missing_scripts:
            print(f"\n⚠️  缺少测试脚本: {', '.join(missing_scripts)}")
            return False
        
        print("✅ 所有测试脚本检查通过")
        return True
    
    def run_script(self, script_name):
        """运行单个测试脚本"""
        print(f"\n{'='*60}")
        print(f"🏃 运行测试脚本: {script_name}")
        print('='*60)
        
        script_path = os.path.join(self.project_root, script_name)
        
        try:
            start_time = time.time()
            
            # 运行脚本
            result = subprocess.run(
                [sys.executable, script_path],
                cwd=self.project_root,
                capture_output=False,  # 直接显示输出
                text=True,
                timeout=300  # 5分钟超时
            )
            
            end_time = time.time()
            duration = round(end_time - start_time, 2)
            
            if result.returncode == 0:
                print(f"\n✅ {script_name} 执行成功 (耗时: {duration}秒)")
                return True
            else:
                print(f"\n❌ {script_name} 执行失败 (返回码: {result.returncode})")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"\n⏰ {script_name} 执行超时")
            return False
        except Exception as e:
            print(f"\n❌ {script_name} 执行出错: {e}")
            return False
    
    def compile_and_check(self):
        """编译检查Python脚本"""
        print("\n🔧 编译检查测试脚本...")
        
        for script in self.test_scripts:
            script_path = os.path.join(self.project_root, script)
            
            try:
                # 编译检查
                with open(script_path, 'r', encoding='utf-8') as f:
                    source_code = f.read()
                
                compile(source_code, script_path, 'exec')
                print(f"  ✅ {script} - 编译通过")
                
            except SyntaxError as e:
                print(f"  ❌ {script} - 语法错误: {e}")
                return False
            except Exception as e:
                print(f"  ❌ {script} - 编译错误: {e}")
                return False
        
        print("✅ 所有脚本编译检查通过")
        return True
    
    def run_all_tests(self):
        """运行所有测试"""
        self.print_header()
        
        # 检查依赖
        if not self.check_dependencies():
            print("\n❌ 依赖检查失败，无法继续")
            return False
        
        # 检查脚本
        if not self.check_scripts():
            print("\n❌ 脚本检查失败，无法继续")
            return False
        
        # 编译检查
        if not self.compile_and_check():
            print("\n❌ 编译检查失败，无法继续")
            return False
        
        # 运行测试脚本
        success_count = 0
        total_count = len(self.test_scripts)
        
        for script in self.test_scripts:
            if self.run_script(script):
                success_count += 1
            
            # 脚本间等待
            if script != self.test_scripts[-1]:
                print("\n⏳ 等待3秒后运行下一个测试...")
                time.sleep(3)
        
        # 总结
        print(f"\n{'='*80}")
        print("📊 测试运行总结")
        print('='*80)
        print(f"总测试数: {total_count}")
        print(f"成功数: {success_count}")
        print(f"失败数: {total_count - success_count}")
        print(f"成功率: {(success_count/total_count)*100:.1f}%")
        
        if success_count == total_count:
            print("🎉 所有测试运行成功！")
            return True
        else:
            print("⚠️  部分测试失败，请检查输出信息")
            return False
    
    def show_results(self):
        """显示测试结果文件"""
        print(f"\n📁 查找测试结果文件...")
        
        result_files = []
        for file in os.listdir(self.project_root):
            if file.startswith(('ssl_risk_test_results_', 'ssl_certificate_analysis_')):
                result_files.append(file)
        
        if result_files:
            print("找到以下结果文件:")
            for file in sorted(result_files, reverse=True):  # 按时间倒序
                file_path = os.path.join(self.project_root, file)
                file_size = os.path.getsize(file_path)
                mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                print(f"  📄 {file} ({file_size} bytes, {mod_time.strftime('%Y-%m-%d %H:%M:%S')})")
        else:
            print("未找到测试结果文件")

def main():
    """主函数"""
    runner = SSLTestRunner()
    
    try:
        success = runner.run_all_tests()
        
        # 显示结果文件
        runner.show_results()
        
        if success:
            print(f"\n🎯 SSL测试完成！请查看生成的结果文件了解详细信息。")
            return 0
        else:
            print(f"\n❌ SSL测试未完全成功，请检查错误信息。")
            return 1
            
    except KeyboardInterrupt:
        print(f"\n⏹️  用户中断测试")
        return 1
    except Exception as e:
        print(f"\n💥 运行器出错: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
