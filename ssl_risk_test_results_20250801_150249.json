{"https://www.huhhothome.cn": {"cert_info": {"success": true, "cert_info": {"subject": {"commonName": "www.huhhothome.cn"}, "issuer": {"countryName": "US", "organizationName": "DigiCert Inc", "organizationalUnitName": "www.digicert.com", "commonName": "Encryption Everywhere DV TLS CA - G2"}, "version": 3, "serial_number": "012778AA72BAC4E4A3B8F730C2B2516E", "not_before": "Jun 14 00:00:00 2025 GMT", "not_after": "Jun 13 23:59:59 2026 GMT", "cipher_suite": "ECDHE-RSA-AES256-GCM-SHA384", "tls_version": "TLSv1.2", "key_bits": 256}, "days_until_expiry": 316, "warnings": []}, "requests_test": {"ssl_enabled": {"success": true, "status_code": 200, "response_time": 0.032, "headers": {"Date": "Fri, 01 Aug 2025 07:02:49 GMT", "Content-Type": "text/html", "Content-Length": "424", "Connection": "keep-alive", "Server": "nginx", "Last-Modified": "Fri, 14 Jul 2023 06:20:13 GMT", "ETag": "\"64b0e91d-1a8\"", "P3P": "CAO PSA OUR", "Accept-Ranges": "bytes"}}, "ssl_disabled": {"success": true, "status_code": 200, "response_time": 0.022}}, "aiohttp_test": {"ssl_enabled": {"success": true, "status_code": 200, "response_time": 0.016, "content_length": 402}, "ssl_disabled": {"success": true, "status_code": 200, "response_time": 0.015}}}, "https://www.huhhothome.cn/api/dynamicapi/apiview/viewdata": {"cert_info": {"success": true, "cert_info": {"subject": {"commonName": "www.huhhothome.cn"}, "issuer": {"countryName": "US", "organizationName": "DigiCert Inc", "organizationalUnitName": "www.digicert.com", "commonName": "Encryption Everywhere DV TLS CA - G2"}, "version": 3, "serial_number": "012778AA72BAC4E4A3B8F730C2B2516E", "not_before": "Jun 14 00:00:00 2025 GMT", "not_after": "Jun 13 23:59:59 2026 GMT", "cipher_suite": "ECDHE-RSA-AES256-GCM-SHA384", "tls_version": "TLSv1.2", "key_bits": 256}, "days_until_expiry": 316, "warnings": []}, "requests_test": {"ssl_enabled": {"success": true, "status_code": 500, "response_time": 0.026, "headers": {"Date": "Fri, 01 Aug 2025 07:02:49 GMT", "Content-Type": "application/json", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Server": "nginx", "vary": "accept-encoding"}}, "ssl_disabled": {"success": true, "status_code": 500, "response_time": 0.031}}, "aiohttp_test": {"ssl_enabled": {"success": true, "status_code": 500, "response_time": 0.023, "content_length": 114}, "ssl_disabled": {"success": true, "status_code": 500, "response_time": 0.024}}}, "https://api.huhhothome.cn": {"cert_info": {"success": false, "error": "SSL错误: [SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'api.huhhothome.cn'. (_ssl.c:997)", "error_type": "ssl_error"}, "requests_test": {"ssl_enabled": {"success": false, "error": "HTTPSConnectionPool(host='api.huhhothome.cn', port=443): Max retries exceeded with url: / (Caused by SSLError(CertificateError(\"hostname 'api.huhhothome.cn' doesn't match either of 'www.huhhothome.cn', 'huhhothome.cn'\")))", "error_type": "ssl_error"}, "ssl_disabled": {"success": true, "status_code": 200, "response_time": 0.136}}, "aiohttp_test": {"ssl_enabled": {"success": false, "error": "Cannot connect to host api.huhhothome.cn:443 ssl:True [SSLCertVerificationError: (1, \"[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: Hostname mismatch, certificate is not valid for 'api.huhhothome.cn'. (_ssl.c:997)\")]", "error_type": "ssl_error"}, "ssl_disabled": {"success": true, "status_code": 200, "response_time": 0.032}}}, "proxy_compatibility": {"skipped": true, "reason": "未配置测试代理"}, "risk_analysis": {"risks": ["❌ https://api.huhhothome.cn: SSL证书验证失败", "⚠️  SSL验证失败率: 33.3% (2/6)"], "recommendations": ["建议为 https://api.huhhothome.cn 配置证书例外或使用自定义CA", "可以考虑启用SSL验证，但需要异常处理"]}}