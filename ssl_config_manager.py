#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL配置管理器 - 青城住房监控系统
基于风险分析结果的SSL验证配置管理方案
"""

import ssl
import os
import yaml
import json
from typing import Dict, Any, Optional, Union
from enum import Enum
from dataclasses import dataclass
import logging

class SSLVerificationMode(Enum):
    """SSL验证模式"""
    DISABLED = "disabled"           # 完全禁用SSL验证
    STRICT = "strict"              # 严格验证（默认）
    RELAXED = "relaxed"            # 宽松验证（忽略主机名不匹配）
    CUSTOM = "custom"              # 自定义验证策略

@dataclass
class SSLConfig:
    """SSL配置数据类"""
    verification_mode: SSLVerificationMode = SSLVerificationMode.STRICT
    verify_hostname: bool = True
    ca_bundle_path: Optional[str] = None
    client_cert_path: Optional[str] = None
    client_key_path: Optional[str] = None
    allowed_hosts: list = None
    fallback_to_insecure: bool = False
    timeout: int = 10
    
    def __post_init__(self):
        if self.allowed_hosts is None:
            self.allowed_hosts = []

class SSLConfigManager:
    """SSL配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "ssl_config.yaml"
        self.logger = logging.getLogger(__name__)
        self._ssl_configs = {}
        self._load_config()
    
    def _load_config(self):
        """加载SSL配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                    self._parse_config(config_data)
            else:
                self._create_default_config()
        except Exception as e:
            self.logger.error(f"加载SSL配置失败: {e}")
            self._create_default_config()
    
    def _parse_config(self, config_data: Dict[str, Any]):
        """解析配置数据"""
        environments = config_data.get('ssl_environments', {})
        
        for env_name, env_config in environments.items():
            ssl_config = SSLConfig(
                verification_mode=SSLVerificationMode(env_config.get('verification_mode', 'strict')),
                verify_hostname=env_config.get('verify_hostname', True),
                ca_bundle_path=env_config.get('ca_bundle_path'),
                client_cert_path=env_config.get('client_cert_path'),
                client_key_path=env_config.get('client_key_path'),
                allowed_hosts=env_config.get('allowed_hosts', []),
                fallback_to_insecure=env_config.get('fallback_to_insecure', False),
                timeout=env_config.get('timeout', 10)
            )
            self._ssl_configs[env_name] = ssl_config
    
    def _create_default_config(self):
        """创建默认配置"""
        # 基于风险分析结果的默认配置
        default_configs = {
            'development': SSLConfig(
                verification_mode=SSLVerificationMode.RELAXED,
                verify_hostname=False,
                fallback_to_insecure=True,
                allowed_hosts=['www.huhhothome.cn', 'api.huhhothome.cn']
            ),
            'testing': SSLConfig(
                verification_mode=SSLVerificationMode.RELAXED,
                verify_hostname=True,
                fallback_to_insecure=True,
                allowed_hosts=['www.huhhothome.cn']
            ),
            'production': SSLConfig(
                verification_mode=SSLVerificationMode.STRICT,
                verify_hostname=True,
                fallback_to_insecure=False,
                allowed_hosts=['www.huhhothome.cn']
            )
        }
        
        self._ssl_configs = default_configs
        self._save_config()
    
    def _save_config(self):
        """保存配置到文件"""
        try:
            config_data = {
                'ssl_environments': {}
            }
            
            for env_name, ssl_config in self._ssl_configs.items():
                config_data['ssl_environments'][env_name] = {
                    'verification_mode': ssl_config.verification_mode.value,
                    'verify_hostname': ssl_config.verify_hostname,
                    'ca_bundle_path': ssl_config.ca_bundle_path,
                    'client_cert_path': ssl_config.client_cert_path,
                    'client_key_path': ssl_config.client_key_path,
                    'allowed_hosts': ssl_config.allowed_hosts,
                    'fallback_to_insecure': ssl_config.fallback_to_insecure,
                    'timeout': ssl_config.timeout
                }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
                
            self.logger.info(f"SSL配置已保存到: {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"保存SSL配置失败: {e}")
    
    def get_ssl_config(self, environment: str = 'development') -> SSLConfig:
        """获取指定环境的SSL配置"""
        return self._ssl_configs.get(environment, self._ssl_configs.get('development'))
    
    def create_ssl_context(self, environment: str = 'development') -> Union[ssl.SSLContext, bool]:
        """创建SSL上下文"""
        config = self.get_ssl_config(environment)
        
        if config.verification_mode == SSLVerificationMode.DISABLED:
            return False
        
        # 创建SSL上下文
        context = ssl.create_default_context()
        
        if config.verification_mode == SSLVerificationMode.RELAXED:
            context.check_hostname = False
            context.verify_mode = ssl.CERT_REQUIRED
        elif config.verification_mode == SSLVerificationMode.STRICT:
            context.check_hostname = config.verify_hostname
            context.verify_mode = ssl.CERT_REQUIRED
        
        # 加载自定义CA证书
        if config.ca_bundle_path and os.path.exists(config.ca_bundle_path):
            context.load_verify_locations(config.ca_bundle_path)
        
        # 加载客户端证书
        if config.client_cert_path and config.client_key_path:
            if os.path.exists(config.client_cert_path) and os.path.exists(config.client_key_path):
                context.load_cert_chain(config.client_cert_path, config.client_key_path)
        
        return context
    
    def get_requests_verify_param(self, environment: str = 'development') -> Union[bool, str]:
        """获取requests库的verify参数"""
        config = self.get_ssl_config(environment)
        
        if config.verification_mode == SSLVerificationMode.DISABLED:
            return False
        elif config.ca_bundle_path and os.path.exists(config.ca_bundle_path):
            return config.ca_bundle_path
        else:
            return True
    
    def should_verify_hostname(self, hostname: str, environment: str = 'development') -> bool:
        """检查是否应该验证特定主机名"""
        config = self.get_ssl_config(environment)
        
        # 如果在允许列表中，根据配置决定
        if hostname in config.allowed_hosts:
            return config.verify_hostname
        
        # 默认验证
        return True
    
    def can_fallback_to_insecure(self, environment: str = 'development') -> bool:
        """检查是否可以降级到不安全连接"""
        config = self.get_ssl_config(environment)
        return config.fallback_to_insecure
    
    def update_environment_config(self, environment: str, **kwargs):
        """更新环境配置"""
        if environment not in self._ssl_configs:
            self._ssl_configs[environment] = SSLConfig()
        
        config = self._ssl_configs[environment]
        
        for key, value in kwargs.items():
            if hasattr(config, key):
                if key == 'verification_mode' and isinstance(value, str):
                    setattr(config, key, SSLVerificationMode(value))
                else:
                    setattr(config, key, value)
        
        self._save_config()
    
    def get_environment_from_config(self) -> str:
        """从环境变量或配置文件获取当前环境"""
        # 优先级：环境变量 > 配置文件 > 默认值
        env = os.environ.get('SSL_ENVIRONMENT')
        if env and env in self._ssl_configs:
            return env
        
        # 检查是否有生产环境标识
        if os.environ.get('PRODUCTION') == '1':
            return 'production'
        elif os.environ.get('TESTING') == '1':
            return 'testing'
        else:
            return 'development'

# 全局SSL配置管理器实例
_ssl_config_manager = None

def get_ssl_config_manager() -> SSLConfigManager:
    """获取全局SSL配置管理器实例"""
    global _ssl_config_manager
    if _ssl_config_manager is None:
        _ssl_config_manager = SSLConfigManager()
    return _ssl_config_manager

def create_ssl_context_for_environment(environment: str = None) -> Union[ssl.SSLContext, bool]:
    """为指定环境创建SSL上下文的便捷函数"""
    manager = get_ssl_config_manager()
    if environment is None:
        environment = manager.get_environment_from_config()
    return manager.create_ssl_context(environment)

def get_requests_verify_for_environment(environment: str = None) -> Union[bool, str]:
    """为指定环境获取requests verify参数的便捷函数"""
    manager = get_ssl_config_manager()
    if environment is None:
        environment = manager.get_environment_from_config()
    return manager.get_requests_verify_param(environment)

# 示例使用
if __name__ == '__main__':
    # 创建SSL配置管理器
    manager = SSLConfigManager()
    
    # 获取当前环境
    current_env = manager.get_environment_from_config()
    print(f"当前环境: {current_env}")
    
    # 获取SSL配置
    config = manager.get_ssl_config(current_env)
    print(f"SSL配置: {config}")
    
    # 创建SSL上下文
    ssl_context = manager.create_ssl_context(current_env)
    print(f"SSL上下文: {ssl_context}")
    
    # 获取requests verify参数
    verify_param = manager.get_requests_verify_param(current_env)
    print(f"Requests verify参数: {verify_param}")
