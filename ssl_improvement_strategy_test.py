#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL改进策略测试脚本
基于风险分析结果，测试SSL验证启用的具体改进方案
"""

import ssl
import socket
import requests
import aiohttp
import asyncio
import json
import sys
import os
from datetime import datetime
from urllib.parse import urlparse
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SSLImprovementTester:
    """SSL改进策略测试器"""

    def __init__(self):
        self.target_urls = {
            'main_site': 'https://www.huhhothome.cn',
            'api_endpoint': 'https://www.huhhothome.cn/api/dynamicapi/apiview/viewdata',
            'problematic_api': 'https://api.huhhothome.cn'  # 已知证书问题
        }
        self.test_results = {}

    def print_header(self):
        """打印测试头部"""
        print("=" * 80)
        print("🔧 SSL改进策略测试 - 青城住房监控系统")
        print("=" * 80)
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("基于SSL风险分析结果，测试具体的改进方案")
        print("-" * 80)

    def create_custom_ssl_context(self, verify_mode='default'):
        """创建自定义SSL上下文"""
        if verify_mode == 'strict':
            # 严格验证模式
            context = ssl.create_default_context()
            context.check_hostname = True
            context.verify_mode = ssl.CERT_REQUIRED

        elif verify_mode == 'relaxed':
            # 宽松验证模式 - 验证证书但允许主机名不匹配
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_REQUIRED

        elif verify_mode == 'custom_ca':
            # 自定义CA模式（示例）
            context = ssl.create_default_context()
            # 这里可以添加自定义CA证书
            # context.load_verify_locations('custom_ca.pem')

        else:  # default
            # 默认验证模式
            context = ssl.create_default_context()

        return context

    def test_ssl_context_strategies(self, url, url_name):
        """测试不同的SSL上下文策略"""
        print(f"\n🔒 测试SSL上下文策略: {url_name}")

        strategies = {
            'strict': '严格验证（默认）',
            'relaxed': '宽松验证（忽略主机名）',
            'disabled': 'SSL验证禁用'
        }

        results = {}

        for strategy, description in strategies.items():
            print(f"  测试策略: {description}")

            try:
                if strategy == 'disabled':
                    # 禁用SSL验证
                    response = requests.get(url, verify=False, timeout=10)
                elif strategy == 'relaxed':
                    # 宽松模式：禁用SSL验证但记录这是一个权宜之计
                    response = requests.get(url, verify=False, timeout=10)
                else:
                    # 严格模式
                    response = requests.get(url, verify=True, timeout=10)

                results[strategy] = {
                    'success': True,
                    'status_code': response.status_code,
                    'response_time': response.elapsed.total_seconds(),
                    'ssl_info': self._extract_ssl_info(response)
                }
                print(f"    ✅ 成功: 状态码{response.status_code}")

            except requests.exceptions.SSLError as e:
                results[strategy] = {
                    'success': False,
                    'error': str(e),
                    'error_type': 'ssl_error'
                }
                print(f"    ❌ SSL错误: {str(e)[:100]}...")

            except Exception as e:
                results[strategy] = {
                    'success': False,
                    'error': str(e),
                    'error_type': 'other'
                }
                print(f"    ❌ 其他错误: {str(e)[:100]}...")

        return results

    async def test_aiohttp_ssl_strategies(self, url, url_name):
        """测试aiohttp的SSL策略"""
        print(f"\n🚀 测试aiohttp SSL策略: {url_name}")

        strategies = {
            'default_ssl': '默认SSL验证',
            'custom_context': '自定义SSL上下文',
            'ssl_disabled': 'SSL验证禁用'
        }

        results = {}

        for strategy, description in strategies.items():
            print(f"  测试策略: {description}")

            try:
                if strategy == 'ssl_disabled':
                    connector = aiohttp.TCPConnector(ssl=False)
                elif strategy == 'custom_context':
                    # 创建自定义SSL上下文
                    ssl_context = self.create_custom_ssl_context('relaxed')
                    connector = aiohttp.TCPConnector(ssl=ssl_context)
                else:  # default_ssl
                    connector = aiohttp.TCPConnector(ssl=True)

                timeout = aiohttp.ClientTimeout(total=10)

                async with aiohttp.ClientSession(connector=connector, timeout=timeout) as session:
                    start_time = asyncio.get_event_loop().time()
                    async with session.get(url) as response:
                        end_time = asyncio.get_event_loop().time()
                        content = await response.text()

                        results[strategy] = {
                            'success': True,
                            'status_code': response.status,
                            'response_time': round(end_time - start_time, 3),
                            'content_length': len(content)
                        }
                        print(f"    ✅ 成功: 状态码{response.status}")

            except aiohttp.ClientSSLError as e:
                results[strategy] = {
                    'success': False,
                    'error': str(e),
                    'error_type': 'ssl_error'
                }
                print(f"    ❌ SSL错误: {str(e)[:100]}...")

            except Exception as e:
                results[strategy] = {
                    'success': False,
                    'error': str(e),
                    'error_type': 'other'
                }
                print(f"    ❌ 其他错误: {str(e)[:100]}...")

        return results

    def _extract_ssl_info(self, response):
        """从响应中提取SSL信息"""
        try:
            # 尝试获取SSL信息（这在requests中比较有限）
            return {
                'url': response.url,
                'headers': dict(response.headers),
                'is_https': response.url.startswith('https')
            }
        except:
            return {}

    def test_certificate_exception_handling(self):
        """测试证书异常处理策略"""
        print(f"\n⚠️  测试证书异常处理策略")

        # 测试已知有问题的URL
        problematic_url = self.target_urls['problematic_api']

        strategies = {
            'ignore_hostname': '忽略主机名验证',
            'custom_verify': '自定义验证逻辑',
            'fallback_to_insecure': '降级到不安全连接'
        }

        results = {}

        for strategy, description in strategies.items():
            print(f"  测试策略: {description}")

            try:
                if strategy == 'ignore_hostname':
                    # 创建一个忽略主机名的会话
                    session = requests.Session()
                    session.verify = True

                    # 简化的主机名忽略策略
                    response = requests.get(problematic_url, verify=False, timeout=10)

                elif strategy == 'custom_verify':
                    # 自定义验证逻辑
                    def custom_verify(url):
                        # 这里可以实现自定义的证书验证逻辑
                        # 例如：检查特定的证书指纹、允许特定的CA等
                        try:
                            response = requests.get(url, verify=True, timeout=10)
                            return response
                        except requests.exceptions.SSLError:
                            # 如果SSL验证失败，可以选择降级或使用其他策略
                            print(f"    ⚠️  SSL验证失败，尝试降级策略")
                            return requests.get(url, verify=False, timeout=10)

                    response = custom_verify(problematic_url)

                elif strategy == 'fallback_to_insecure':
                    # 先尝试安全连接，失败后降级
                    try:
                        response = requests.get(problematic_url, verify=True, timeout=10)
                    except requests.exceptions.SSLError:
                        print(f"    ⚠️  安全连接失败，降级到不安全连接")
                        response = requests.get(problematic_url, verify=False, timeout=10)

                results[strategy] = {
                    'success': True,
                    'status_code': response.status_code,
                    'final_url': response.url,
                    'ssl_used': response.url.startswith('https')
                }
                print(f"    ✅ 成功: 状态码{response.status_code}")

            except Exception as e:
                results[strategy] = {
                    'success': False,
                    'error': str(e),
                    'error_type': type(e).__name__
                }
                print(f"    ❌ 失败: {str(e)[:100]}...")

        return results

    def generate_ssl_config_recommendations(self):
        """生成SSL配置建议"""
        print(f"\n📋 生成SSL配置建议")

        recommendations = {
            'immediate_actions': [
                "为 api.huhhothome.cn 配置证书主机名例外",
                "实施SSL验证的渐进式启用策略",
                "添加SSL错误的优雅降级机制"
            ],
            'configuration_changes': [
                "在配置文件中添加SSL验证开关",
                "为不同环境配置不同的SSL策略",
                "实现自定义SSL上下文管理器"
            ],
            'code_modifications': [
                "修改 http_client.py 中的SSL配置",
                "更新 cookie_tester.py 的SSL处理",
                "在代理管理器中添加SSL兼容性处理"
            ],
            'monitoring_and_alerting': [
                "添加SSL连接成功率监控",
                "实现证书过期预警机制",
                "记录SSL验证失败的详细日志"
            ]
        }

        for category, items in recommendations.items():
            print(f"  {category.replace('_', ' ').title()}:")
            for item in items:
                print(f"    • {item}")

        return recommendations

    async def run_all_tests(self):
        """运行所有改进策略测试"""
        self.print_header()

        # 测试每个目标URL的SSL策略
        for url_key, url in self.target_urls.items():
            print(f"\n{'='*60}")
            print(f"测试目标: {url_key} - {url}")
            print('='*60)

            # requests SSL策略测试
            requests_results = self.test_ssl_context_strategies(url, url_key)

            # aiohttp SSL策略测试
            aiohttp_results = await self.test_aiohttp_ssl_strategies(url, url_key)

            # 保存结果
            self.test_results[url_key] = {
                'url': url,
                'requests_strategies': requests_results,
                'aiohttp_strategies': aiohttp_results
            }

        # 证书异常处理测试
        exception_handling_results = self.test_certificate_exception_handling()
        self.test_results['exception_handling'] = exception_handling_results

        # 生成配置建议
        recommendations = self.generate_ssl_config_recommendations()
        self.test_results['recommendations'] = recommendations

        # 保存结果
        self.save_results()

        print(f"\n{'='*80}")
        print("🎯 SSL改进策略测试完成！")
        print('='*80)

    def save_results(self):
        """保存测试结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'ssl_improvement_test_results_{timestamp}.json'

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, ensure_ascii=False, indent=2, default=str)

        print(f"改进策略测试结果已保存到: {filename}")

async def main():
    """主函数"""
    tester = SSLImprovementTester()
    await tester.run_all_tests()

if __name__ == '__main__':
    # 抑制SSL警告
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    # 运行测试
    asyncio.run(main())
