#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SSL证书深度分析脚本
专门分析目标网站的SSL证书详细信息和潜在问题
"""

import ssl
import socket
import requests
import subprocess
import sys
import os
import json
from datetime import datetime
from urllib.parse import urlparse
import warnings
warnings.filterwarnings('ignore')

class SSLCertificateAnalyzer:
    """SSL证书深度分析器"""
    
    def __init__(self):
        self.target_domains = [
            'www.huhhothome.cn',
            'api.huhhothome.cn',
            'huhhothome.cn'
        ]
        self.analysis_results = {}
    
    def print_header(self):
        """打印分析头部"""
        print("=" * 80)
        print("🔒 SSL证书深度分析 - 青城住房监控系统")
        print("=" * 80)
        print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"目标域名: {', '.join(self.target_domains)}")
        print("-" * 80)
    
    def get_certificate_chain(self, hostname, port=443):
        """获取完整的证书链"""
        print(f"\n🔍 获取证书链: {hostname}:{port}")
        
        try:
            # 创建SSL上下文，不验证证书（用于获取信息）
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            # 连接并获取证书链
            with socket.create_connection((hostname, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    # 获取对等证书
                    peer_cert = ssock.getpeercert()
                    peer_cert_der = ssock.getpeercert(binary_form=True)
                    
                    # 获取证书链
                    cert_chain = ssock.getpeercert_chain()
                    
                    # 获取连接信息
                    cipher = ssock.cipher()
                    version = ssock.version()
                    
            # 分析证书链
            chain_info = []
            if cert_chain:
                for i, cert_der in enumerate(cert_chain):
                    cert_pem = ssl.DER_cert_to_PEM_cert(cert_der)
                    cert_info = self._parse_certificate_details(cert_pem)
                    cert_info['position'] = i
                    cert_info['is_root'] = i == len(cert_chain) - 1
                    chain_info.append(cert_info)
            
            result = {
                'success': True,
                'peer_certificate': self._parse_peer_certificate(peer_cert),
                'certificate_chain': chain_info,
                'connection_info': {
                    'cipher_suite': cipher[0] if cipher else None,
                    'tls_version': version,
                    'key_exchange': cipher[1] if cipher else None,
                    'key_bits': cipher[2] if cipher else None
                },
                'chain_length': len(cert_chain) if cert_chain else 0
            }
            
            print(f"  ✅ 成功获取证书链，共{result['chain_length']}个证书")
            print(f"  ✅ TLS版本: {version}")
            print(f"  ✅ 加密套件: {cipher[0] if cipher else 'Unknown'}")
            
            return result
            
        except ssl.SSLError as e:
            print(f"  ❌ SSL错误: {e}")
            return {'success': False, 'error': str(e), 'error_type': 'ssl_error'}
        except socket.timeout:
            print(f"  ❌ 连接超时")
            return {'success': False, 'error': '连接超时', 'error_type': 'timeout'}
        except Exception as e:
            print(f"  ❌ 其他错误: {e}")
            return {'success': False, 'error': str(e), 'error_type': 'other'}
    
    def _parse_peer_certificate(self, cert):
        """解析对等证书信息"""
        if not cert:
            return None
        
        # 解析主体和颁发者
        subject = dict(x[0] for x in cert.get('subject', []))
        issuer = dict(x[0] for x in cert.get('issuer', []))
        
        # 解析有效期
        not_before = cert.get('notBefore')
        not_after = cert.get('notAfter')
        
        # 计算剩余天数
        days_remaining = None
        if not_after:
            try:
                expiry_date = datetime.strptime(not_after, '%b %d %H:%M:%S %Y %Z')
                days_remaining = (expiry_date - datetime.now()).days
            except:
                pass
        
        # 解析SAN
        san_list = []
        if 'subjectAltName' in cert:
            san_list = [name[1] for name in cert['subjectAltName']]
        
        return {
            'common_name': subject.get('commonName'),
            'organization': subject.get('organizationName'),
            'organizational_unit': subject.get('organizationalUnitName'),
            'country': subject.get('countryName'),
            'state': subject.get('stateOrProvinceName'),
            'locality': subject.get('localityName'),
            'issuer_name': issuer.get('commonName'),
            'issuer_org': issuer.get('organizationName'),
            'serial_number': cert.get('serialNumber'),
            'version': cert.get('version'),
            'not_before': not_before,
            'not_after': not_after,
            'days_remaining': days_remaining,
            'subject_alt_names': san_list,
            'is_self_signed': subject.get('commonName') == issuer.get('commonName')
        }
    
    def _parse_certificate_details(self, cert_pem):
        """解析证书详细信息"""
        try:
            cert = ssl.PEM_cert_to_DER_cert(cert_pem)
            # 这里可以添加更详细的证书解析逻辑
            return {'pem_length': len(cert_pem), 'der_length': len(cert)}
        except:
            return {'parse_error': True}
    
    def check_certificate_trust(self, hostname):
        """检查证书信任状态"""
        print(f"\n🛡️  检查证书信任状态: {hostname}")
        
        try:
            # 使用系统默认的SSL上下文（会验证证书）
            context = ssl.create_default_context()
            
            with socket.create_connection((hostname, 443), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                    print(f"  ✅ 证书受信任，验证通过")
                    return {'trusted': True, 'error': None}
                    
        except ssl.SSLCertVerificationError as e:
            print(f"  ❌ 证书验证失败: {e}")
            return {'trusted': False, 'error': str(e), 'error_type': 'verification_failed'}
        except ssl.SSLError as e:
            print(f"  ❌ SSL错误: {e}")
            return {'trusted': False, 'error': str(e), 'error_type': 'ssl_error'}
        except Exception as e:
            print(f"  ❌ 其他错误: {e}")
            return {'trusted': False, 'error': str(e), 'error_type': 'other'}
    
    def test_ssl_protocols(self, hostname):
        """测试支持的SSL/TLS协议"""
        print(f"\n🔐 测试SSL/TLS协议支持: {hostname}")
        
        protocols = {
            'TLSv1.3': ssl.PROTOCOL_TLS,
            'TLSv1.2': ssl.PROTOCOL_TLS,
            'TLSv1.1': ssl.PROTOCOL_TLS,
            'TLSv1.0': ssl.PROTOCOL_TLS
        }
        
        supported_protocols = {}
        
        for protocol_name, protocol_const in protocols.items():
            try:
                context = ssl.SSLContext(protocol_const)
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                
                # 设置特定协议版本
                if protocol_name == 'TLSv1.3':
                    context.minimum_version = ssl.TLSVersion.TLSv1_3
                    context.maximum_version = ssl.TLSVersion.TLSv1_3
                elif protocol_name == 'TLSv1.2':
                    context.minimum_version = ssl.TLSVersion.TLSv1_2
                    context.maximum_version = ssl.TLSVersion.TLSv1_2
                elif protocol_name == 'TLSv1.1':
                    context.minimum_version = ssl.TLSVersion.TLSv1_1
                    context.maximum_version = ssl.TLSVersion.TLSv1_1
                elif protocol_name == 'TLSv1.0':
                    context.minimum_version = ssl.TLSVersion.TLSv1
                    context.maximum_version = ssl.TLSVersion.TLSv1
                
                with socket.create_connection((hostname, 443), timeout=5) as sock:
                    with context.wrap_socket(sock, server_hostname=hostname) as ssock:
                        supported_protocols[protocol_name] = {
                            'supported': True,
                            'version': ssock.version(),
                            'cipher': ssock.cipher()
                        }
                        print(f"  ✅ {protocol_name}: 支持")
                        
            except Exception as e:
                supported_protocols[protocol_name] = {
                    'supported': False,
                    'error': str(e)
                }
                print(f"  ❌ {protocol_name}: 不支持 ({str(e)[:50]}...)")
        
        return supported_protocols
    
    def check_certificate_transparency(self, hostname):
        """检查证书透明度日志"""
        print(f"\n📋 检查证书透明度: {hostname}")
        
        try:
            # 简单的CT检查 - 实际实现可能需要更复杂的逻辑
            response = requests.get(f'https://crt.sh/?q={hostname}&output=json', timeout=10)
            if response.status_code == 200:
                ct_logs = response.json()
                print(f"  ✅ 找到{len(ct_logs)}条CT日志记录")
                return {'ct_logs_found': len(ct_logs), 'logs': ct_logs[:5]}  # 只保存前5条
            else:
                print(f"  ⚠️  CT查询失败: HTTP {response.status_code}")
                return {'ct_logs_found': 0, 'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            print(f"  ❌ CT检查失败: {e}")
            return {'ct_logs_found': 0, 'error': str(e)}
    
    def analyze_ssl_security(self, hostname):
        """分析SSL安全性"""
        print(f"\n🔒 SSL安全性分析: {hostname}")
        
        security_issues = []
        recommendations = []
        
        # 获取证书链信息
        chain_result = self.analysis_results.get(hostname, {}).get('certificate_chain')
        if chain_result and chain_result.get('success'):
            peer_cert = chain_result.get('peer_certificate', {})
            
            # 检查证书有效期
            days_remaining = peer_cert.get('days_remaining')
            if days_remaining is not None:
                if days_remaining < 0:
                    security_issues.append("证书已过期")
                elif days_remaining < 30:
                    security_issues.append(f"证书即将过期（剩余{days_remaining}天）")
                    recommendations.append("建议监控证书更新")
            
            # 检查自签名证书
            if peer_cert.get('is_self_signed'):
                security_issues.append("使用自签名证书")
                recommendations.append("建议使用CA签发的证书")
            
            # 检查TLS版本
            tls_version = chain_result.get('connection_info', {}).get('tls_version')
            if tls_version:
                if tls_version in ['TLSv1', 'TLSv1.1']:
                    security_issues.append(f"使用过时的TLS版本: {tls_version}")
                    recommendations.append("建议升级到TLS 1.2或1.3")
                elif tls_version == 'TLSv1.2':
                    recommendations.append("建议考虑升级到TLS 1.3")
        
        # 检查证书信任状态
        trust_result = self.analysis_results.get(hostname, {}).get('certificate_trust')
        if trust_result and not trust_result.get('trusted'):
            security_issues.append("证书不受信任")
            recommendations.append("需要配置证书例外或使用受信任的证书")
        
        result = {
            'security_issues': security_issues,
            'recommendations': recommendations,
            'security_score': max(0, 100 - len(security_issues) * 20)  # 简单的评分系统
        }
        
        print(f"  安全评分: {result['security_score']}/100")
        if security_issues:
            print("  发现的问题:")
            for issue in security_issues:
                print(f"    ❌ {issue}")
        if recommendations:
            print("  建议措施:")
            for rec in recommendations:
                print(f"    💡 {rec}")
        
        return result
    
    def run_analysis(self):
        """运行完整分析"""
        self.print_header()
        
        for hostname in self.target_domains:
            print(f"\n{'='*60}")
            print(f"分析域名: {hostname}")
            print('='*60)
            
            # 获取证书链
            chain_result = self.get_certificate_chain(hostname)
            
            # 检查证书信任
            trust_result = self.check_certificate_trust(hostname)
            
            # 测试SSL协议
            protocol_result = self.test_ssl_protocols(hostname)
            
            # 检查证书透明度
            ct_result = self.check_certificate_transparency(hostname)
            
            # 保存结果
            self.analysis_results[hostname] = {
                'certificate_chain': chain_result,
                'certificate_trust': trust_result,
                'supported_protocols': protocol_result,
                'certificate_transparency': ct_result
            }
            
            # 安全性分析
            security_result = self.analyze_ssl_security(hostname)
            self.analysis_results[hostname]['security_analysis'] = security_result
        
        # 保存结果
        self.save_results()
        
        print(f"\n{'='*80}")
        print("🎯 SSL证书分析完成！")
        print('='*80)
    
    def save_results(self):
        """保存分析结果"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'ssl_certificate_analysis_{timestamp}.json'
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.analysis_results, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"分析结果已保存到: {filename}")

def main():
    """主函数"""
    analyzer = SSLCertificateAnalyzer()
    analyzer.run_analysis()

if __name__ == '__main__':
    # 抑制警告
    import urllib3
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    
    main()
